#!/bin/bash

echo "Installing Bull Queue dependencies..."

# Install Bull and Redis dependencies
npm install @nestjs/bull bull redis

# Install types for development
npm install --save-dev @types/bull

echo "Queue dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Make sure <PERSON><PERSON> is running on your system"
echo "2. Add Redis configuration to your .env file:"
echo "   REDIS_HOST=localhost"
echo "   REDIS_PORT=6379"
echo "   REDIS_PASSWORD=your_redis_password (if needed)"
echo ""
echo "3. Start Redis (if not already running):"
echo "   - macOS: brew services start redis"
echo "   - Ubuntu: sudo systemctl start redis"
echo "   - Docker: docker run -d -p 6379:6379 redis:alpine"
