# Email Queue Implementation

## Problem Solved

The original implementation had email sending blocking the user registration flow, causing:
- Slow user registration (waiting for SMTP connection)
- ECONNECT and ETIMEDOUT errors
- Poor user experience
- No retry mechanism for failed emails

## Solution: Background Email Queue

This implementation uses **Bull Queue** with **Redis** to process emails asynchronously with automatic retries.

## Architecture

```
User Registration Request
         ↓
    Create User Account
         ↓
    Queue Email Job (instant)
         ↓
    Return Success Response
         ↓
Background Worker Processes Email
         ↓
    Automatic Retries on Failure
```

## Key Benefits

1. **Fast Registration**: User creation returns immediately
2. **Reliability**: Automatic retries with exponential backoff
3. **Monitoring**: Queue statistics and failed job management
4. **Scalability**: Can handle high email volumes
5. **Fault Tolerance**: Emails won't be lost if SMTP fails temporarily

## Installation

1. Install dependencies:
```bash
chmod +x install-queue-dependencies.sh
./install-queue-dependencies.sh
```

2. Start Redis:
```bash
# macOS
brew services start redis

# Ubuntu
sudo systemctl start redis

# Docker
docker run -d -p 6379:6379 redis:alpine
```

3. Update your `.env` file:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```

## Configuration

### Retry Strategy
- **Verification emails**: 5 attempts with exponential backoff (2s, 4s, 8s, 16s, 32s)
- **General emails**: 3 attempts with exponential backoff (1s, 2s, 4s)

### SMTP Improvements
- Connection timeout: 10 seconds
- Socket timeout: 10 seconds
- Connection pooling enabled
- Maximum 5 concurrent connections

## Monitoring

### Queue Statistics
```bash
GET /queue-monitor/stats
```
Returns:
```json
{
  "waiting": 5,
  "active": 2,
  "completed": 150,
  "failed": 3
}
```

### Retry Failed Jobs
```bash
POST /queue-monitor/retry-failed
```

## Usage

### Before (Blocking)
```typescript
// This would block for 10-30 seconds on SMTP errors
await this.emailService.sendVerificationEmail(email, token, name);
```

### After (Non-blocking)
```typescript
// This returns immediately
await this.emailQueueService.addVerificationEmailJob(email, token, name, userId);
```

## Error Handling

The queue processor handles common SMTP errors:
- `ECONNECTION`: Connection refused
- `ETIMEDOUT`: Connection timeout
- `ENOTFOUND`: DNS resolution failed

Failed jobs are automatically retried with exponential backoff.

## Production Considerations

1. **Redis Persistence**: Configure Redis with persistence for production
2. **Monitoring**: Set up alerts for high failure rates
3. **Scaling**: Add more worker processes for high volume
4. **Dead Letter Queue**: Handle permanently failed jobs

## Testing

Test the queue with a simple email:
```typescript
await emailQueueService.addEmailJob(
  '<EMAIL>',
  'Test Subject',
  '<h1>Test Email</h1>'
);
```

Monitor the queue stats to see job processing.
