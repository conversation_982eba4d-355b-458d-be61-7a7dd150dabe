import { Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { EmailQueueService } from '../services/email-queue.service';
import { Roles } from '../../auth/decorators/roles.decorator';
import { RolesGuard } from '../../auth/guards/roles.guard';

@ApiTags('queue-monitor')
@ApiBearerAuth()
@Controller('queue-monitor')
export class QueueMonitorController {
  constructor(private readonly emailQueueService: EmailQueueService) {}

  @Get('stats')
  @Roles('admin')
  @UseGuards(RolesGuard)
  @ApiOperation({ summary: 'Get email queue statistics' })
  @ApiResponse({
    status: 200,
    description: 'Queue statistics retrieved successfully',
    schema: {
      properties: {
        waiting: { type: 'number' },
        active: { type: 'number' },
        completed: { type: 'number' },
        failed: { type: 'number' },
      },
    },
  })
  async getQueueStats() {
    return this.emailQueueService.getQueueStats();
  }

  @Post('retry-failed')
  @Roles('admin')
  @UseGuards(RolesGuard)
  @ApiOperation({ summary: 'Retry all failed email jobs' })
  @ApiResponse({
    status: 200,
    description: 'Failed jobs have been queued for retry',
  })
  async retryFailedJobs() {
    await this.emailQueueService.retryFailedJobs();
    return { message: 'Failed jobs have been queued for retry' };
  }
}
