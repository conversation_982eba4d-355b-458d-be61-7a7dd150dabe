import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

export interface EmailJobData {
  to: string;
  subject: string;
  html: string;
  text?: string;
  type: 'verification' | 'general';
  userId?: number;
  token?: string;
  name?: string;
}

@Injectable()
export class EmailQueueService {
  private readonly logger = new Logger(EmailQueueService.name);

  constructor(@InjectQueue('email') private emailQueue: Queue) {}

  /**
   * Add a verification email to the queue
   */
  async addVerificationEmailJob(
    to: string,
    token: string,
    name: string,
    userId: number,
  ): Promise<void> {
    try {
      const jobData: EmailJobData = {
        to,
        subject: 'Verify Your Email Address',
        html: '', // Will be generated in processor
        type: 'verification',
        userId,
        token,
        name,
      };

      await this.emailQueue.add('send-verification-email', jobData, {
        attempts: 5, // Retry up to 5 times
        backoff: {
          type: 'exponential',
          delay: 2000, // Start with 2 seconds, then 4, 8, 16, 32
        },
        removeOnComplete: 10, // Keep only last 10 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs for debugging
      });

      this.logger.log(`Verification email job queued for ${to}`);
    } catch (error) {
      this.logger.error(`Failed to queue verification email for ${to}`, error);
      throw error;
    }
  }

  /**
   * Add a general email to the queue
   */
  async addEmailJob(
    to: string,
    subject: string,
    html: string,
    text?: string,
  ): Promise<void> {
    try {
      const jobData: EmailJobData = {
        to,
        subject,
        html,
        text,
        type: 'general',
      };

      await this.emailQueue.add('send-email', jobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
        removeOnComplete: 5,
        removeOnFail: 20,
      });

      this.logger.log(`Email job queued for ${to}`);
    } catch (error) {
      this.logger.error(`Failed to queue email for ${to}`, error);
      throw error;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    const waiting = await this.emailQueue.getWaiting();
    const active = await this.emailQueue.getActive();
    const completed = await this.emailQueue.getCompleted();
    const failed = await this.emailQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  /**
   * Retry failed jobs
   */
  async retryFailedJobs(): Promise<void> {
    const failedJobs = await this.emailQueue.getFailed();
    
    for (const job of failedJobs) {
      await job.retry();
      this.logger.log(`Retrying failed email job ${job.id}`);
    }
  }
}
