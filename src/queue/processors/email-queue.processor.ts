import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EmailService } from '../../email/email.service';
import { EmailJobData } from '../services/email-queue.service';
import { ConfigService } from '@nestjs/config';

@Processor('email')
export class EmailQueueProcessor {
  private readonly logger = new Logger(EmailQueueProcessor.name);

  constructor(
    private emailService: EmailService,
    private configService: ConfigService,
  ) {}

  @Process('send-verification-email')
  async handleVerificationEmail(job: Job<EmailJobData>) {
    const { to, token, name, userId } = job.data;
    
    try {
      this.logger.log(`Processing verification email for ${to} (Job ${job.id})`);
      
      // Generate the verification email HTML
      const verificationUrl = `${this.configService.get<string>(
        'FRONTEND_URL',
        'http://localhost:3001',
      )}/auth/verify/${token}`;

      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Welcome to Art Saving Lifes!</h2>
          <p>Hello ${name},</p>
          <p>Thank you for signing up. Please verify your email address by clicking the button below:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              Verify Email
            </a>
          </div>
          <p>Or copy and paste this link into your browser:</p>
          <p>${verificationUrl}</p>
          <p>This link will expire in 24 hours.</p>
          <p>If you did not sign up for an account, please ignore this email.</p>
          <p>Best regards,<br>The Art Saving Lifes Team</p>
        </div>
      `;

      await this.emailService.sendEmail(to, 'Verify Your Email Address', html);
      
      this.logger.log(`Verification email sent successfully to ${to} (Job ${job.id})`);
      
      // Update job progress
      await job.progress(100);
      
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${to} (Job ${job.id})`,
        error,
      );
      
      // Log the specific error for debugging
      if (error.code === 'ECONNECTION' || error.code === 'ETIMEDOUT') {
        this.logger.error('SMTP connection issue detected', {
          code: error.code,
          command: error.command,
          response: error.response,
        });
      }
      
      throw error; // This will trigger a retry
    }
  }

  @Process('send-email')
  async handleGeneralEmail(job: Job<EmailJobData>) {
    const { to, subject, html, text } = job.data;
    
    try {
      this.logger.log(`Processing email for ${to} (Job ${job.id})`);
      
      await this.emailService.sendEmail(to, subject, html, text);
      
      this.logger.log(`Email sent successfully to ${to} (Job ${job.id})`);
      
      await job.progress(100);
      
    } catch (error) {
      this.logger.error(
        `Failed to send email to ${to} (Job ${job.id})`,
        error,
      );
      
      if (error.code === 'ECONNECTION' || error.code === 'ETIMEDOUT') {
        this.logger.error('SMTP connection issue detected', {
          code: error.code,
          command: error.command,
          response: error.response,
        });
      }
      
      throw error;
    }
  }
}
