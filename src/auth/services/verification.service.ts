import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { VerificationTokenRepository } from '../entities/verification-token.repository';
import { VerificationToken } from '../entities/verification-token.entity';
import { UsersService } from '../../users/users.service';
import { EmailQueueService } from '../../queue/services/email-queue.service';

@Injectable()
export class VerificationService {
  private readonly logger = new Logger(VerificationService.name);

  constructor(
    private verificationTokenRepository: VerificationTokenRepository,
    private usersService: UsersService,
    private emailQueueService: EmailQueueService,
  ) {}

  /**
   * Create a verification token for a user and send a verification email
   * @param userId The ID of the user
   * @param type The type of verification token (default: 'email')
   * @param expiresInHours The number of hours until the token expires (default: 24)
   * @returns The created verification token
   */
  async createVerificationToken(
    userId: number,
    type = 'email',
    expiresInHours = 24,
  ): Promise<VerificationToken> {
    try {
      // Get the user to send the email
      const user = await this.usersService.findOne(userId);

      // Generate a random token
      const token = uuidv4();

      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + expiresInHours);

      // Create and save the token
      const verificationToken = this.verificationTokenRepository.create({
        token,
        type,
        expiresAt,
        userId,
      });

      const savedToken =
        await this.verificationTokenRepository.save(verificationToken);

      // Queue verification email for background processing
      try {
        await this.emailQueueService.addVerificationEmailJob(
          user.email,
          token,
          `${user.firstName} ${user.lastName}`,
          user.id,
        );
        this.logger.log(`Verification email queued for ${user.email}`);
      } catch (error) {
        this.logger.error(
          `Failed to queue verification email for ${user.email}`,
          error,
        );
        // We don't throw here because we still want to return the token
        // even if the email queueing fails
      }

      return savedToken;
    } catch (error) {
      this.logger.error(
        `Failed to create verification token for user ${userId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Verify a token and mark the user as verified
   * @param token The token to verify
   * @returns The user that was verified
   */
  async verifyToken(token: string): Promise<boolean> {
    try {
      // Find the token
      const verificationToken = await this.verificationTokenRepository.findOne({
        where: { token },
        relations: ['user'],
      });

      if (!verificationToken) {
        this.logger.warn(`Verification token not found: ${token}`);
        throw new NotFoundException('Verification token not found');
      }

      // Check if the token has expired
      if (verificationToken.expiresAt < new Date()) {
        this.logger.warn(`Verification token has expired: ${token}`);
        throw new Error('Verification token has expired');
      }

      // Mark the user as verified
      const user = await this.usersService.findOne(verificationToken.userId);
      user.verifiedAt = new Date();
      await this.usersService.update(user.id, { verifiedAt: user.verifiedAt });

      this.logger.log(`User ${user.email} (ID: ${user.id}) has been verified`);

      // Delete the token
      await this.verificationTokenRepository.remove(verificationToken);

      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error verifying token: ${token}`, error);
      throw error;
    }
  }

  /**
   * Get a verification token by its token string
   * @param token The token string
   * @returns The verification token
   */
  async getVerificationToken(token: string): Promise<VerificationToken> {
    const verificationToken = await this.verificationTokenRepository.findOne({
      where: { token },
      relations: ['user'],
    });

    if (!verificationToken) {
      throw new NotFoundException('Verification token not found');
    }

    return verificationToken;
  }

  /**
   * Resend verification email to a user
   * @param email The email address of the user
   */
  async resendVerificationEmail(email: string): Promise<void> {
    try {
      // Find the user by email
      const user = await this.usersService.findByEmail(email);

      // Check if user is already verified
      if (user.verifiedAt) {
        this.logger.warn(
          `Attempt to resend verification email for already verified user: ${email}`,
        );
        throw new Error('This email is already verified');
      }

      // Delete any existing tokens for this user
      await this.verificationTokenRepository.delete({ userId: user.id });
      this.logger.log(
        `Deleted existing verification tokens for user: ${email}`,
      );

      // Create a new verification token and send email
      await this.createVerificationToken(user.id);
      this.logger.log(`Resent verification email to: ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to resend verification email to ${email}`,
        error,
      );
      throw error;
    }
  }
}
