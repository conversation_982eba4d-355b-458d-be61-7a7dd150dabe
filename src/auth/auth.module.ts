import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RolesService } from './services/roles.service';
import { PermissionsService } from './services/permissions.service';
import { AuthService } from './services/auth.service';
import { RolesController } from './controllers/roles.controller';
import { PermissionsController } from './controllers/permissions.controller';
import { AuthController } from './controllers/auth.controller';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard } from './guards/roles.guard';
import { RoleRepository } from './entities/role.repository';
import { PermissionRepository } from './entities/permission.repository';
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { VerificationToken } from './entities/verification-token.entity';
import { VerificationTokenRepository } from './entities/verification-token.repository';
import { VerificationService } from './services/verification.service';
import { UsersModule } from '../users/users.module';
import { QueueModule } from '../queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Role, Permission, VerificationToken]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: '1d',
        },
      }),
    }),
    UsersModule,
    QueueModule,
  ],
  providers: [
    RolesService,
    PermissionsService,
    VerificationService,
    AuthService,
    RoleRepository,
    PermissionRepository,
    VerificationTokenRepository,
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
  controllers: [AuthController, PermissionsController, RolesController],
  exports: [AuthService, PermissionsService, RolesService, VerificationService],
})
export class AuthModule {}
